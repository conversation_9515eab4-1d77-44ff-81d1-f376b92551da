import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/models/events/categories_model.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/utils/cache_keys.dart';

class Eventcontroller extends GetxController implements GetxService {
  final events = <Event>[].obs;
  RxList<MyEventsModel> userEvents = <MyEventsModel>[].obs;
  final logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();

  RxBool isLoading = false.obs;
  RxBool isLoadingUser = false.obs;

  final page = 0.obs;
  final userpage = 0.obs;

  final maxPage = 0.obs;
  final maxUserPage = 0.obs;

  //filter
  final RxString search = "".obs;
  final Rx<int?> filterCategory = Rx<int?>(null);
  final RxString startDate = ''.obs;
  final RxString endDate = ''.obs;
  final RxString status = ''.obs;
  final RxString selectedStatus = "ACTIVE".obs;
  final selectedCategory = Rxn<CategoriesModel>();
  final isApplyingFilters = false.obs;

  UserModelLatest? getLocalUser() {
    final box = Get.find<GetStorage>();
    Rx<UserModelLatest> user = UserModelLatest().obs;
    final usr = box.read(CacheKeys.user);
    if (usr != null) {
      user(UserModelLatest.fromJson(usr));
      return user.value;
    } else {
      return null;
    }
  }

  void resetEvents() {
    events.clear();
    page.value = 0;
    maxPage.value = 0;
  }

  void resetUserEvents() {
    userEvents.clear();
    userpage.value = 0;
    maxUserPage.value = 0;
  }

  Future<void> fetchEvents() async {
    if (page.value > maxPage.value) return;
    if (isLoading.value) return;

    try {
      isLoading(true);
      final response = await apiProvider.request(
        method: Method.GET,
        url:
            "${ApiUrls.GETALLEVENTS}?size=5&page=${page.value}&search=${search.value}"
            "&category_id=${filterCategory.value ?? ''}&start-date=${startDate.value}"
            "&end-date=${endDate.value}&status=${status.value}",
      );

      if (response.data != null && response.data['data'] != null) {
        final returnedData = response.data['data']['items'] as List;
        final newEvents =
            returnedData.map((item) => Event.fromJson(item)).toList();

        page.value = response.data['data']['page'] + 1;
        maxPage.value = response.data['data']['total_pages'];
        events.addAll(newEvents);
      }
    } catch (e) {
      logger.e('Error fetching events: $e');
      if (Get.context != null) {
        ToastUtils.showErrorToast(Get.context!, 'Error', 'Failed to fetch events: ${e.toString()}');
      }
    } finally {
      isLoading(false);
    }
  }

  Future<void> fetchUserEvents() async {
    if (userpage.value > maxUserPage.value) return;
    if (isLoadingUser.value) return;

    try {
      isLoading(true);
      final response = await apiProvider.request(
        method: Method.GET,
        url:
            "${ApiUrls.GETUSEREVENTS}/${getLocalUser()?.phoneNumber}?size=5&page=${userpage.value}&search=${search.value}"
            "&category_id=${filterCategory.value ?? ''}&start-date=${startDate.value}"
            "&end-date=${endDate.value}&status=${status.value}",
      );

      if (response.data['status'] ?? false) {
        final returnedData = (response.data['data']['items'] ?? []) as List;
        final newEvents =
            returnedData.map((item) => MyEventsModel.fromJson(item)).toList();

        userpage.value = response.data['data']['page'] + 1;
        maxUserPage.value = response.data['data']['total_pages'];
        userEvents.addAll(newEvents);
      } else {
        Get.snackbar(
            '', response.data['message'] ?? 'error retrieving user events',
            backgroundColor: const Color(0xFFFF0000));
      }
    } catch (e) {
      logger.e('Error fetching user events: $e');
      if (Get.context != null) {
        ToastUtils.showErrorToast(Get.context!, 'Error', 'Failed to fetch user events: ${e.toString()}');
      }
    } finally {
      isLoadingUser(false);
    }
  }
}
