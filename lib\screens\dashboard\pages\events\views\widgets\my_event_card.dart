import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/view_single_event_organizer.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/attendees_widget.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/my_quill_editor.dart' show QuillEditorShortWidget;
import 'package:onekitty/utils/show_cached_network_image.dart';
import 'package:onekitty/utils/utils_exports.dart';
import '../../../../../../utils/event_details.dart';
import 'package:get/get.dart';
import '../../../../../../main.dart' as main show isLight;
import 'package:onekitty/screens/dashboard/pages/events/controllers/view_single_event.dart';

// Custom page route for smoother hero animations
class HeroPageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  
  HeroPageRoute({required this.page})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 600),
          reverseTransitionDuration: const Duration(milliseconds: 500),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // Fade animation
            var fadeAnimation = CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
            );
            
            // Scale animation
            var scaleAnimation = Tween<double>(
              begin: 0.95,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            ));
            
            return FadeTransition(
              opacity: fadeAnimation,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: child,
              ),
            );
          },
        );
}

class MyEventCards extends StatelessWidget {
  final bool? organizer;
  final MyEventsModel eventModel;
  const MyEventCards({super.key, required this.eventModel, this.organizer});

  @override
  Widget build(BuildContext context) {
    final event = eventModel.event;
    // Create a unique suffix for the Hero tag to avoid conflicts
    final String uniqueSuffix = DateTime.now().microsecondsSinceEpoch.toString();
    
    return GestureDetector(
      onTap: () {
        // Update the event model with the hero tag suffix before navigating
        final updatedEventModel = MyEventsModel(
          event: event,
          count: eventModel.count,
          hasSignatoryTransactions: eventModel.hasSignatoryTransactions,
          heroTagSuffix: uniqueSuffix
        );
        
        Get.put(ViewSingleEventController()).event(event);
        
        // Use custom page route for smoother hero animations
        Navigator.push(
          context,
          HeroPageRoute(
            page: ViewSingleEventOrganizer(
              eventmodel: updatedEventModel,
            ),
          ),
        );
      },
      child: ValueListenableBuilder(
        valueListenable: main.isLight,
        builder:(context, isLight,_) =>Card(
          elevation: 4,
          margin: const EdgeInsets.all(8),
          color: isLight ? Colors.white : const Color(0xff26262e),
          child: Padding(
            padding: EdgeInsets.only(
              left: 6.spMin,
              right: 6.spMin,
              top: 6.spMin,
              bottom: 10.spMin,
            ),
            child: Column(
              children: [
                Hero(
                  tag: 'image:${event.id}o:card:$uniqueSuffix',
                  child: AdaptiveCachedNetworkImage(
                    imageUrl: event.eventMedia == null ||
                            event.eventMedia!.isEmpty
                        ? AssetUrl.onekittyBannnerUrl
                        : event.eventMedia?[0].url ?? AssetUrl.onekittyBannnerUrl,
                    initialWidth: 390.w,
                    borderRadius: 8.r,
                    initialHeight: 300.h,
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Material(
                        color: Colors.transparent,
                        textStyle: TextStyle(
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8.0),
                              child: Hero(
                                tag: 'text:${event.id}o:$uniqueSuffix',
                                child: Material(
                                  color: Colors.transparent,
                                  child: Text(event.title,
                                      style:   TextStyle(
                                          color: main.isLight.value ? Colors.black : Colors.white,
                                          fontWeight: FontWeight.w700,
                                          fontSize: 20)),
                                ),
                              ),
                            ),
                            EventDetailsWidget(
                                id: "location${event.id}o:$uniqueSuffix",
                                label: "${event.locationTip} - ${event.venue}",
                                image: 'assets/images/icons/location.png',
                                icon: Icons.location_on_outlined),
                            EventDetailsWidget(
                                id: "date${event.id}o:$uniqueSuffix",
                                image: 'assets/images/icons/calendar.png',
                                label:
                                    '${formatDate("${event.startDate?.toLocal()}")}, ${formatTime("${event.startDate?.toLocal()}")}',
                                icon: Icons.calendar_month),
                          ],
                        ),
                      ),
                    ),
                    if (organizer ?? false)
                      Hero(
                        tag: 'collected${event.id}o:$uniqueSuffix',
                        child: Material(
                          color: Colors.transparent,
                          child: Text.rich(
                              textAlign: TextAlign.end,
                              TextSpan(children: [
                                TextSpan(
                                    text: "${FormattedCurrency
                                            .getFormattedCurrency(
                                                eventModel.event.balance ?? 0.0)}\n",
                                    style: const TextStyle(
                                        color: Color(0xff4355b6),
                                        fontSize: 30,
                                        fontWeight: FontWeight.w600)),
                                const TextSpan(text: 'Collected')
                              ])),
                        ),
                      )
                  ],
                ),
                Align(
                    alignment: Alignment.topLeft,
                    child: QuillEditorShortWidget(
                      maxLines: 2, 
                      text: event.description,
                      tag: 'event_${event.id}_$uniqueSuffix', // Add unique tag
                    )),
                if (organizer ?? false)
                  AttendeesWidget(
                    count: eventModel.count,
                    size: 18,
                    textSize: 15,
                  )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
